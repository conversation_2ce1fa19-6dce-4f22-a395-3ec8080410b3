# 导入所需的库
import numpy as np
import pandas as pd
import datetime
import math

# 导入聚宽API函数
from jqdata import *

# 初始化函数
def initialize(context):
    """初始化函数，设置基本参数和运行环境"""
    # 设置动态复权模式
    set_option('use_real_price', True)
    # 设置日志级别为 INFO
    log.set_level('strategy', 'info')

    # 设置基准
    set_benchmark('000300.XSHG')  # 沪深300作为基准

    # 设置滑点
    set_slippage(FixedSlippage(0.002))

    # 设置手续费
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=0), type='stock')

    # 设置主要ETF池
    context.main_etfs = [
        '510300.XSHG',  # 沪深300ETF
        '513100.XSHG',  # 纳斯达克100ETF
        '513000.XSHG',  # 日经225ETF
        '513730.XSHG',  # 东南亚科技ETF
        '513080.XSHG',  # 法国CAC40ETF
        '513030.XSHG',  # 德国DAX ETF
        '518880.XSHG',  # 黄金ETF
    ]

    # 设置货币基金ETF
    context.money_fund_etf = '511880.XSHG'  # 银华日利ETF

    # 设置动量周期（交易日）
    context.momentum_periods_days = [21, 63, 126]  # 分别近似1个月、3个月、6个月的交易日

    # 设置动量权重
    context.momentum_weights = [0.4, 0.3, 0.3]  # 对应上述周期的动量权重

    # 设置波动率计算周期
    context.volatility_period_days = 60  # 波动率计算周期

    # 设置因子权重
    context.factor_weights = {'momentum': 0.8, 'volatility': 0.2}  # 最终综合评分中，动量和波动率的权重

    # 设置选择ETF数量
    context.top_n_etfs = 3  # 选择排名前N的ETF

    # 设置Z-score的Winsorize上下限
    context.winsorize_limits_zscore = (-3.0, 3.0)

    # 设置运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间
    context.initial_rebalance_done = False  # 首次建仓是否已完成的标志

    # 设置每月第一个交易日执行调仓
    run_monthly(rebalance_portfolio, 1, time='10:00')

# 调仓函数
def rebalance_portfolio(context):
    """每月第一个交易日执行调仓逻辑"""
    log.info("开始执行月度调仓...")

    # 获取当前日期
    current_date = context.current_dt.date()

    # 检查是否已经在本月调仓过
    if context.last_rebalance_date is not None:
        if context.last_rebalance_date.year == current_date.year and context.last_rebalance_date.month == current_date.month:
            log.info("本月已经调仓，跳过")
            return

    # 计算各ETF的因子值
    etf_factors = calculate_etf_factors(context, context.main_etfs)

    # 如果没有有效的ETF因子数据，则全部资金买入货币基金
    if etf_factors.empty or len(etf_factors) == 0:
        log.info("没有有效的ETF因子数据，全部资金买入货币基金")
        # 计算总资产价值
        total_portfolio_value = context.portfolio.total_value
        # 全仓买入货币基金
        order_target_value(context.money_fund_etf, total_portfolio_value)
        # 清仓所有ETF
        for etf in context.main_etfs:
            if context.portfolio.positions.get(etf, None):
                order_target_value(etf, 0)
        context.last_rebalance_date = current_date
        return

    # 打印因子值
    print_factor_values(etf_factors)

    # 根据最终评分排序，选择前N个ETF
    top_etfs = etf_factors.sort_values('final_score', ascending=False).head(context.top_n_etfs)

    # 计算总调整分数
    sum_adjusted_scores = top_etfs['adjusted_score'].sum()

    # 如果总调整分数小于等于0，则全部资金买入货币基金
    if sum_adjusted_scores <= 0:
        log.info("所有ETF的调整分数都为0或负数，全部资金买入货币基金")
        # 计算总资产价值
        total_portfolio_value = context.portfolio.total_value
        # 全仓买入货币基金
        order_target_value(context.money_fund_etf, total_portfolio_value)
        # 清仓所有ETF
        for etf in context.main_etfs:
            if context.portfolio.positions.get(etf, None):
                order_target_value(etf, 0)
        context.last_rebalance_date = current_date
        return

    # 计算各ETF的权重
    weights = {}
    for idx, row in top_etfs.iterrows():
        weights[idx] = row['adjusted_score'] / sum_adjusted_scores

    # 执行调仓
    log.info("执行调仓操作:")

    # 计算总资产价值
    total_portfolio_value = context.portfolio.total_value

    # 获取当前交易日期
    current_date = context.current_dt.date()

    # 获取当前市场数据，用于检查价格有效性和停牌状态
    current_data = get_current_data()
    log.info(f"当前时间: {context.current_dt}, 获取市场数据完成")

    # 先清仓所有不在top_etfs中的ETF或权重为0的ETF
    for etf in context.main_etfs:
        # 检查是否需要清仓：不在weights中或在weights中但权重为0
        need_clear = (etf not in weights or weights.get(etf, 0) == 0) and context.portfolio.positions.get(etf, None)

        if need_clear:
            # 检查ETF是否已上市
            security_info = get_security_info(etf)
            if security_info.start_date <= current_date:
                # 检查停牌状态
                etf_data = current_data.get(etf)

                # 添加详细的调试日志
                log.info(f"调试信息 - ETF: {etf}, get_current_data().get(etf) 返回: {etf_data}")
                if etf_data:
                    log.info(f"调试信息 - ETF: {etf}, Paused: {etf_data.paused}, LastPrice: {etf_data.last_price}, DayOpen: {getattr(etf_data, 'day_open', 'N/A')}")
                    # 尝试获取更多属性
                    try:
                        log.info(f"调试信息 - ETF: {etf}, 额外属性: is_st={getattr(etf_data, 'is_st', 'N/A')}, high_limit={getattr(etf_data, 'high_limit', 'N/A')}, low_limit={getattr(etf_data, 'low_limit', 'N/A')}")
                    except Exception as e:
                        log.info(f"获取ETF {etf}额外属性时出错: {str(e)}")

                if etf_data and not etf_data.paused:  # 主要判断条件：未停牌
                    # 记录一下当前价格信息，用于调试
                    if np.isnan(etf_data.last_price):
                        log.info(f"ETF {etf}: 当前分钟 last_price is NaN, 但未停牌，尝试清仓。")
                    else:
                        log.info(f"ETF {etf}: 当前价格 {etf_data.last_price}, 未停牌，尝试清仓。")

                    # 执行清仓操作
                    context.order_target_value(etf, 0)
                    log.info(f"清仓 {etf}")
                else:
                    paused_status = etf_data.paused if etf_data else "N/A (no current data)"
                    log.warning(f"ETF {etf} 已暂停交易 (Paused: {paused_status}) 或无当前数据，跳过清仓。")
            else:
                log.info(f"{etf} 尚未上市，跳过清仓操作。")

    # 清仓货币基金（如果持有）
    if context.portfolio.positions.get(context.money_fund_etf, None):
        # 货币基金通常不会有上市日期或价格问题，但为了代码一致性，也加上检查
        security_info = get_security_info(context.money_fund_etf)
        if security_info.start_date <= current_date:
            etf_data = current_data.get(context.money_fund_etf)

            if etf_data and not etf_data.paused:  # 主要判断条件：未停牌
                # 记录一下当前价格信息，用于调试
                if np.isnan(etf_data.last_price):
                    log.info(f"货币基金 {context.money_fund_etf}: 当前分钟 last_price is NaN, 但未停牌，尝试清仓。")
                else:
                    log.info(f"货币基金 {context.money_fund_etf}: 当前价格 {etf_data.last_price}, 未停牌，尝试清仓。")

                # 执行清仓操作
                context.order_target_value(context.money_fund_etf, 0)
                log.info(f"清仓货币基金 {context.money_fund_etf}")
            else:
                paused_status = etf_data.paused if etf_data else "N/A (no current data)"
                log.warning(f"货币基金 {context.money_fund_etf} 已暂停交易 (Paused: {paused_status}) 或无当前数据，跳过清仓。")
        else:
            log.info(f"货币基金 {context.money_fund_etf} 尚未上市，跳过清仓操作。")

    # 买入top_etfs
    for etf, weight_percentage in weights.items():
        # 跳过权重为0的ETF，因为已在前面的清仓逻辑中处理
        if weight_percentage <= 0:
            continue

        # 检查ETF是否已上市
        security_info = get_security_info(etf)
        if security_info.start_date <= current_date:
            # 检查停牌状态
            etf_data = current_data.get(etf)

            # 添加详细的调试日志
            log.info(f"调试信息 - ETF: {etf}, get_current_data().get(etf) 返回: {etf_data}")
            if etf_data:
                log.info(f"调试信息 - ETF: {etf}, Paused: {etf_data.paused}, LastPrice: {etf_data.last_price}, DayOpen: {getattr(etf_data, 'day_open', 'N/A')}")
                # 尝试获取更多属性
                try:
                    log.info(f"调试信息 - ETF: {etf}, 额外属性: is_st={getattr(etf_data, 'is_st', 'N/A')}, high_limit={getattr(etf_data, 'high_limit', 'N/A')}, low_limit={getattr(etf_data, 'low_limit', 'N/A')}")
                except Exception as e:
                    log.info(f"获取ETF {etf}额外属性时出错: {str(e)}")

            if etf_data and not etf_data.paused:  # 主要判断条件：未停牌
                # 记录一下当前价格信息，用于调试
                if np.isnan(etf_data.last_price):
                    log.info(f"ETF {etf}: 当前分钟 last_price is NaN, 但未停牌，尝试买入。")
                else:
                    log.info(f"ETF {etf}: 当前价格 {etf_data.last_price}, 未停牌，尝试买入。")

                # 执行买入操作
                target_etf_value = total_portfolio_value * weight_percentage
                context.order_target_value(etf, target_etf_value)
                log.info(f"买入 {etf}, 目标市值: {target_etf_value:.2f}, 目标权重: {weight_percentage:.2%}")
            else:
                paused_status = etf_data.paused if etf_data else "N/A (no current data)"
                log.warning(f"ETF {etf} 已暂停交易 (Paused: {paused_status}) 或无当前数据，跳过买入。")
        else:
            log.info(f"{etf} 尚未上市，跳过买入操作。")

    # 更新最后调仓日期
    context.last_rebalance_date = current_date
    log.info("月度调仓完成")

def calculate_etf_factors(context, etf_list):
    """计算ETF的因子值"""
    # 创建一个空的DataFrame来存储因子值
    columns = ['momentum_score', 'volatility_score', 'momentum_zscore', 'volatility_zscore',
               'final_score', 'adjusted_score']
    etf_factors = pd.DataFrame(index=etf_list, columns=columns)

    # 遍历ETF列表，计算各因子值
    valid_etfs = []
    for etf in etf_list:
        # 检查ETF是否已上市
        security_info = get_security_info(etf)
        # 将 context.current_dt (datetime) 转换为 date 类型，与 security_info.start_date 保持一致
        current_date = context.current_dt.date()
        if security_info.start_date > current_date:
            log.info(f"{etf} 尚未上市，跳过")
            continue

        # 计算动量因子
        momentum_score = get_momentum_score(context, etf, context.momentum_periods_days, context.momentum_weights)

        # 如果动量因子无效（数据不足），则跳过该ETF
        if np.isnan(momentum_score):
            log.info(f"{etf} 动量因子计算无效（数据不足），跳过")
            continue

        # 计算波动率因子
        volatility = get_volatility(context, etf, context.volatility_period_days)

        # 如果波动率因子无效，则跳过该ETF
        if np.isnan(volatility):
            log.info(f"{etf} 波动率因子计算无效（数据不足），跳过")
            continue

        # 存储原始因子值
        etf_factors.loc[etf, 'momentum_score'] = momentum_score
        etf_factors.loc[etf, 'volatility_score'] = volatility

        # 添加到有效ETF列表
        valid_etfs.append(etf)

    # 如果没有有效的ETF，则返回空DataFrame
    if len(valid_etfs) == 0:
        return pd.DataFrame()

    # 只保留有效的ETF
    etf_factors = etf_factors.loc[valid_etfs]

    # 对动量和波动率因子进行Z-score标准化和Winsorize处理
    etf_factors['momentum_zscore'] = z_score_and_winsorize(etf_factors['momentum_score'], context.winsorize_limits_zscore)
    etf_factors['volatility_zscore'] = z_score_and_winsorize(etf_factors['volatility_score'], context.winsorize_limits_zscore)

    # 计算最终评分：动量Z-score越高越好，波动率Z-score越低越好
    etf_factors['final_score'] = (
        context.factor_weights['momentum'] * etf_factors['momentum_zscore'] -
        context.factor_weights['volatility'] * etf_factors['volatility_zscore']
    )

    # 计算调整后的分数（非负）
    etf_factors['adjusted_score'] = etf_factors['final_score'].apply(lambda x: max(0, x))

    return etf_factors

def get_momentum_score(context, etf, periods, weights):
    """计算单个ETF的动量分数"""
    # 获取最长周期的历史数据
    max_period = max(periods)

    try:
        # 获取历史价格数据
        price_data = get_price(etf, count=max_period+1, frequency='daily', fields=['close'])
        close_prices = price_data['close']

        # 如果数据长度不足最短周期，则返回NaN
        if len(close_prices) < min(periods) + 1:
            log.info(f"{etf} 数据长度不足最短周期 {min(periods)} 天，无法计算动量")
            return np.nan

        # 计算各周期动量
        momentum_values = []
        valid_weights = []

        for i, period in enumerate(periods):
            if len(close_prices) >= period + 1:
                # 计算动量：(当前价格 / N日前价格) - 1
                momentum = (close_prices.iloc[-1] / close_prices.iloc[-period-1]) - 1
                momentum_values.append(momentum)
                valid_weights.append(weights[i])
            else:
                log.info(f"{etf} 数据长度不足 {period} 天，跳过该周期动量计算")

        # 如果没有有效的动量值，则返回NaN
        if len(momentum_values) == 0:
            return np.nan

        # 归一化权重
        valid_weights = [w/sum(valid_weights) for w in valid_weights]

        # 计算加权动量分数
        momentum_score = sum(m * w for m, w in zip(momentum_values, valid_weights))

        return momentum_score

    except Exception as e:
        log.error(f"计算 {etf} 动量分数时出错: {str(e)}")
        return np.nan

def get_volatility(context, etf, period):
    """计算单个ETF的波动率"""
    try:
        # 获取历史价格数据
        price_data = get_price(etf, count=period+1, frequency='daily', fields=['close'])
        close_prices = price_data['close']

        # 如果数据长度不足，则返回NaN
        if len(close_prices) < period:
            log.info(f"{etf} 数据长度不足 {period} 天，无法计算波动率")
            return np.nan

        # 计算日收益率
        returns = close_prices.pct_change().dropna()

        # 计算波动率（标准差）
        volatility = returns.std()

        return volatility

    except Exception as e:
        log.error(f"计算 {etf} 波动率时出错: {str(e)}")
        return np.nan

def z_score_and_winsorize(series, limits):
    """对序列进行Z-score标准化和Winsorize处理"""
    try:
        # 计算均值和标准差
        mean = series.mean()
        std = series.std()

        # 如果标准差为0，则返回全0序列
        if std == 0:
            return pd.Series(0, index=series.index)

        # 计算Z-score
        z_scores = (series - mean) / std

        # Winsorize处理
        lower_limit, upper_limit = limits
        z_scores = z_scores.apply(lambda x: max(lower_limit, min(upper_limit, x)))

        return z_scores

    except Exception as e:
        log.error(f"Z-score标准化和Winsorize处理时出错: {str(e)}")
        return pd.Series(0, index=series.index)

def print_factor_values(etf_factors):
    """打印ETF因子值，用于调试"""
    log.info("ETF因子值:")
    for etf in etf_factors.index:
        log.info(f"  {etf}:")
        log.info(f"    动量分数: {etf_factors.loc[etf, 'momentum_score']:.4f}")
        log.info(f"    波动率: {etf_factors.loc[etf, 'volatility_score']:.4f}")
        log.info(f"    动量Z-score: {etf_factors.loc[etf, 'momentum_zscore']:.4f}")
        log.info(f"    波动率Z-score: {etf_factors.loc[etf, 'volatility_zscore']:.4f}")
        log.info(f"    最终评分: {etf_factors.loc[etf, 'final_score']:.4f}")
        log.info(f"    调整后评分: {etf_factors.loc[etf, 'adjusted_score']:.4f}")

def handle_data(context, data):
    """
    每个交易日运行，用于处理交易数据
    在策略回测开始的第一个交易日执行首次建仓，但推迟到10:00执行
    每周一输出当前持仓状态
    """
    # 检查是否需要执行首次建仓，推迟到10:00执行
    if not context.initial_rebalance_done:
        current_time = context.current_dt.time()
        if current_time >= datetime.time(10, 0):  # 推迟到10:00执行，与run_monthly相同时间
            log.info(f"执行首次建仓... (时间: {current_time})")
            rebalance_portfolio(context)  # 调用调仓函数进行首次建仓
            context.initial_rebalance_done = True  # 标记首次建仓已完成
            log.info("首次建仓完成")

    # 每周一输出当前持仓状态
    if context.current_dt.weekday() == 0 and context.current_dt.hour < 10:
        log.info("当前持仓状态:")
        total_value = context.portfolio.total_value
        for security in context.portfolio.positions:
            position = context.portfolio.positions[security]
            if position.total_amount > 0:
                value_percent = position.value / total_value * 100
                log.info(f"  {security}: 持仓 {position.total_amount} 股, 市值 {position.value:.2f}, 占比 {value_percent:.2f}%")

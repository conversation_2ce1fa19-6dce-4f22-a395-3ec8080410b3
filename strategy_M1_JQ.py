# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime
import math
import talib

# 初始化函数
def initialize(context):
    """初始化函数"""
    # 设置动态复权模式
    set_option('use_real_price', True)
    # 设置日志级别为 DEBUG
    log.set_level('strategy', 'info')
    # 打开防未来函数
    set_option("avoid_future_data", True)

    # 设置基准
    set_benchmark('510300.XSHG')  # 沪深300作为基准

    # 设置滑点
    set_slippage(FixedSlippage(0.002))

    # 更新手续费为万一免5
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0001, close_commission=0.0001, min_commission=0), type='stock')

    # 设置交易标的（必须最先定义）
    context.stock_list = ['513100.XSHG',  # 纳斯达克ETF
                         '510300.XSHG',  # 沪深300ETF
                         '518880.XSHG']  # 黄金ETF

    # 设置技术指标参数
    context.bias_period = 24  # BIAS周期
    context.adx_period = 14   # ADX周期
    context.atr_period = 12   # ATR周期

    # 添加动量策略参数
    context.momentum_boost_factor = 0.06  # 动量增强系数
    context.max_momentum_allocation_pct = 0.199    # 最大动量分配比例
    context.momentum_threshold = 0.03    # 动量信号阈值
    context.momentum_M1_lookback = 5     # 定义过去1个月的回溯期 (约21个交易日)


    # 添加缓存字典
    context.cache = {
        'technical_indicators': {},
        'last_update_date': None,
        'price_data': {},
        'last_minute': -1,
        'last_money_fund_trade_date': None,
        'last_volatility_calc': {},  # 新增：缓存波动率计算
        'last_trend_calc': {},       # 新增：缓存趋势计算
        'last_rebalance_check': None, # 新增：缓存再平衡检查
        'last_momentum_check': None   # 新增：缓存动量检查
    }

    # 初始化市场状态字典
    context.market_states = {security: 'oscillating' for security in context.stock_list}

    # 初始化初始买入标志
    context.initial_buy_needed = True

    # 预加载历史数据
    preload_historical_data(context)

    # 设置参数 (更新自 JSON)
    context.total_position_pct = 0.891  # 总仓位
    context.reserve_position_pct = 1 - context.total_position_pct  # 备用资金

    # 设置BIAS阈值
    context.bias_threshold = {
        '513100.XSHG': 0.1,   # 纳斯达克ETF阈值±10%
        '510300.XSHG': 0.1,  # 沪深300ETF阈值±10%
        '518880.XSHG': 0.1   # 黄金ETF阈值±10%
    }

    # 设置风控参数 (更新自 JSON)
    context.single_stock_stop_loss = 0.105  # 单个标的止损
    context.portfolio_stop_loss = 0.06  # 组合止损

    # 设置因子权重 (更新自 JSON)
    context.weight_trend = 0.302
    context.weight_direction = 0.498
    context.weight_volatility = 0.056
    context.weight_volume = 0.058
    context.weight_bias = 0.086

    # 设置趋势调整参数 (更新自 JSON)
    context.uptrend_base_extra = 0.198
    context.downtrend_base_reduction = 0.193
    context.downtrend_pct_limit = 0.309

    # 设置调整系数 (更新自 JSON)
    context.reserve_scale_down_factor = 0.5
    context.reserve_scale_up_factor = 1.25
    context.strong_trend_boost = 1.53
    context.oversold_reduction_factor = 0.930
    context.momentum_strength_factor_multiplier = 1.95

    # BIAS震荡判断放大系数
    context.bias_oscillation_scale_factor = 1.445

    # 动态配置: 趋势持续性计算的ADX基准
    context.alloc_persistence_adx_base =35

    # 置信度计算参数
    context.conf_trend_base = 40
    context.conf_trend_weight = 0.4
    context.conf_bias_mult = 2
    context.conf_bias_weight = 0.3
    context.conf_adx_base = 40
    context.conf_adx_weight = 0.3
    context.conf_vol_offset = 0.5
    context.conf_vol_weight = 0.2

    # 运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间
    context.stop_trading = False  # 是否停止交易的标志

    # 添加货币基金相关参数
    context.money_fund = '511990.XSHG'  # 华宝添益货币ETF
    context.min_fund_amount = 1  # 最小货基交易金额（1元）
    context.money_fund_threshold = 0.01  # 货基交易阈值（1%）

    # 设置定时任务
    run_daily(market_open, time='9:30', reference_security='000300.XSHG')
    run_daily(check_stop_loss, time='9:31', reference_security='000300.XSHG')
    run_daily(trade_money_fund, time='9:32', reference_security='000300.XSHG')
    run_daily(check_fund_income, time='15:10', reference_security='000300.XSHG')
    run_daily(monitor_strategy_performance, time='15:00', reference_security='000300.XSHG')
    run_weekly(apply_momentum_overlay, weekday=1, time='9:35', reference_security='000300.XSHG')  # 新增：每周一动量调整

    # 设置最小交易数量
    context.min_trade_amount = 100

    # 设置是否开启交易日志
    log.set_level('order', 'info')
    log.set_level('strategy', 'info')

    # 设置是否已经完成初始建仓的标志
    context.initial_position_established = False

def preload_historical_data(context):
    """预加载历史数据"""
    try:
        # 获取所需的最长周期
        max_period = max(60, context.bias_period, context.adx_period*2, context.atr_period*2)

        # 一次性获取所有需要的数据
        for security in context.stock_list:
            try:
                # 获取历史数据
                hist_data = attribute_history(security, max_period, '1d',
                                           ['high', 'low', 'close', 'factor', 'volume'],
                                           df=True)

                # 存储到缓存
                context.cache['price_data'][security] = hist_data

                # 计算并缓存初始技术指标
                calculate_initial_indicators(context, security, hist_data)

            except Exception as e:
                log.error(f"预加载 {security} 历史数据时出错: {str(e)}")
                continue

    except Exception as e:
        log.error(f"预加载历史数据时出错: {str(e)}")

def calculate_initial_indicators(context, security, hist_data):
    """计算并缓存初始技术指标"""
    try:
        # 计算真实价格
        hist_data['factor'] = hist_data['factor'].fillna(method='ffill').fillna(1.0)
        hist_data['factor'] = hist_data['factor'].replace(0, 1.0)

        real_closes = hist_data['close'] / hist_data['factor']
        real_highs = hist_data['high'] / hist_data['factor']
        real_lows = hist_data['low'] / hist_data['factor']

        # 使用talib计算ADX
        adx = talib.ADX(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.adx_period)
        plus_di = talib.PLUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.adx_period)
        minus_di = talib.MINUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.adx_period)

        # 使用talib计算ATR
        atr = talib.ATR(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.atr_period)
        new_atr_value = atr[-1]

        # 计算波动率（当前和历史）
        returns = real_closes.pct_change().dropna()
        current_volatility = returns[-20:].std()
        historical_volatility = returns.std()

        # 计算其他指标
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes.mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma20) / ma20

        # 计算平均成交量
        avg_volume = hist_data['volume'][-20:].mean()

        # 计算量比
        current_volume = hist_data['volume'][-1]
        volume_5d_avg = hist_data['volume'][-5:].mean()
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0

        # 计算趋势强度
        trend_strength = adx[-20:].mean()

        # 计算M1动量因子
        try:
            if len(real_closes) >= context.momentum_M1_lookback:
                momentum_M1_value = (real_closes.iloc[-1] / real_closes.iloc[-context.momentum_M1_lookback]) - 1
            else:
                momentum_M1_value = 0.0  # 数据不足时返回0
        except Exception as e:
            log.error(f"计算{security}的M1动量因子时出错: {str(e)}")
            momentum_M1_value = 0.0  # 出错时返回0

        # 存储计算结果
        context.cache['technical_indicators'][security] = {
            'bias': bias,
            'adx': adx[-1] if not np.isnan(adx[-1]) else 25,
            'plus_di': plus_di[-1] if not np.isnan(plus_di[-1]) else 20,
            'minus_di': minus_di[-1] if not np.isnan(minus_di[-1]) else 20,
            'atr': new_atr_value if not np.isnan(new_atr_value) else 0.0,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'real_closes': real_closes,
            'volume_ratio': volume_ratio,
            'momentum_M1': momentum_M1_value
        }

    except Exception as e:
        log.error(f"计算初始技术指标时出错: {str(e)}")

def should_rebalance(context):
    """判断是否需要再平衡"""
    try:
        current_date = context.current_dt.date()

        # 使用缓存检查再平衡
        if context.cache['last_rebalance_check'] == current_date:
            return False

        # 修改为每月最后一个交易日进行再平衡
        current_month = current_date.month
        next_month = current_month + 1 if current_month < 12 else 1
        next_month_first_day = datetime.date(current_date.year + (current_month == 12), next_month, 1)
        current_month_first_day = datetime.date(current_date.year, current_date.month, 1)
        month_trading_days = get_trade_days(start_date=current_month_first_day, end_date=next_month_first_day - datetime.timedelta(days=1))

        if len(month_trading_days) == 0:
            return False

        last_trading_day = month_trading_days[-1]
        if current_date == last_trading_day:
            if context.last_rebalance_date is not None:
                if context.last_rebalance_date.year == current_date.year and context.last_rebalance_date.month == current_date.month:
                    return False
            context.cache['last_rebalance_check'] = current_date
            return True
        return False
    except Exception as e:
        log.error(f"判断再平衡时出错: {str(e)}")
        return False

def calculate_volatility(context, security, period=20):
    """计算波动率，使用缓存优化"""
    try:
        # 检查缓存
        cache_key = f"{security}_{period}"
        if cache_key in context.cache['last_volatility_calc']:
            last_calc = context.cache['last_volatility_calc'][cache_key]
            if last_calc['date'] == context.current_dt.date():
                return last_calc['value']

        # 使用当前日期作为结束日期，避免获取未来数据
        current_date = context.current_dt.date()
        
        # 计算波动率
        if 'technical_indicators' in context.cache and security in context.cache['technical_indicators'] and 'real_closes' in context.cache['technical_indicators'][security]:
            # 使用已缓存的数据
            prices = context.cache['technical_indicators'][security]['real_closes']
            returns = prices.pct_change().dropna()
            # 确保只使用到当前日期为止的数据
            if len(returns) >= period:
                volatility = returns[-period:].std()
            else:
                volatility = returns.std() if len(returns) > 0 else 0.02
        else:
            # 如果没有缓存数据，获取历史数据时明确指定结束日期为当前日期
            try:
                # 使用attribute_history，自动遵循avoid_future_data设置
                hist_data = attribute_history(security, period*2, '1d', ['close'], df=True)
                returns = hist_data['close'].pct_change().dropna()
                volatility = returns.std() if len(returns) > 0 else 0.02
            except Exception as e2:
                log.error(f"获取 {security} 历史数据计算波动率时出错: {str(e2)}")
                volatility = 0.02

        # 更新缓存
        context.cache['last_volatility_calc'][cache_key] = {
            'date': current_date,
            'value': volatility
        }

        return volatility
    except Exception as e:
        log.error(f"计算 {security} 波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def get_trend_strength(context, security, period=20):
    """计算趋势强度，使用缓存优化"""
    try:
        # 检查缓存
        cache_key = f"{security}_{period}"
        if cache_key in context.cache['last_trend_calc']:
            last_calc = context.cache['last_trend_calc'][cache_key]
            if last_calc['date'] == context.current_dt.date():
                return last_calc['value']

        # 使用缓存的趋势强度
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            trend_strength = indicators['trend_strength']
        else:
            update_technical_indicators(context, security)
            indicators = context.cache['technical_indicators'].get(security)
            trend_strength = indicators.get('trend_strength', 20)

        # 更新缓存
        context.cache['last_trend_calc'][cache_key] = {
            'date': context.current_dt.date(),
            'value': trend_strength
        }

        return trend_strength
    except Exception as e:
        log.error(f"计算趋势强度时出错: {str(e)}")
        return 20  # 默认中等趋势

# 优化技术指标计算
def update_technical_indicators(context, security):
    """批量更新技术指标，增加缓存机制和调试日志"""
    current_date = context.current_dt.date()

    # 如果不是新的交易日，且已有缓存，直接返回
    if (context.cache.get('last_update_date') == current_date and
        security in context.cache.get('technical_indicators', {})):
        return context.cache['technical_indicators'][security]

    try:
        # 获取所需的最长周期
        max_period = max(60, context.bias_period, context.adx_period*2, context.atr_period*2)

        # 一次性获取所有需要的数据
        prices = attribute_history(security, max_period, '1d',
                                 ['high', 'low', 'close', 'factor', 'volume'],
                                 df=True)

        # 计算真实价格
        prices['factor'] = prices['factor'].fillna(method='ffill').fillna(1.0)
        prices['factor'] = prices['factor'].replace(0, 1.0)

        real_closes = prices['close'] / prices['factor']
        real_highs = prices['high'] / prices['factor']
        real_lows = prices['low'] / prices['factor']

        # 使用talib计算ADX
        adx = talib.ADX(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.adx_period)
        plus_di = talib.PLUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.adx_period)
        minus_di = talib.MINUS_DI(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.adx_period)

        # 使用talib计算ATR
        atr = talib.ATR(real_highs.values, real_lows.values, real_closes.values, timeperiod=context.atr_period)
        new_atr_value = atr[-1]

        # 计算波动率（当前和历史）
        returns = real_closes.pct_change().dropna()
        current_volatility = returns[-20:].std()
        historical_volatility = returns.std()

        # 计算其他指标
        ma20 = real_closes[-20:].mean()
        ma60 = real_closes.mean()
        latest_price = real_closes[-1]
        bias = (latest_price - ma20) / ma20

        # 计算平均成交量
        avg_volume = prices['volume'][-20:].mean()

        # 计算量比
        current_volume = prices['volume'][-1]
        volume_5d_avg = prices['volume'][-5:].mean()
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0

        # 计算趋势强度
        trend_strength = adx[-20:].mean()

        # 计算M1动量因子
        try:
            if len(real_closes) >= context.momentum_M1_lookback:
                momentum_M1_value = (real_closes.iloc[-1] / real_closes.iloc[-context.momentum_M1_lookback]) - 1
            else:
                momentum_M1_value = 0.0  # 数据不足时返回0
        except Exception as e:
            log.error(f"计算{security}的M1动量因子时出错: {str(e)}")
            momentum_M1_value = 0.0  # 出错时返回0

        # 存储计算结果
        if 'technical_indicators' not in context.cache:
            context.cache['technical_indicators'] = {}

        indicators_to_update = {
            'bias': bias,
            'adx': adx[-1] if not np.isnan(adx[-1]) else 25,
            'plus_di': plus_di[-1] if not np.isnan(plus_di[-1]) else 20,
            'minus_di': minus_di[-1] if not np.isnan(minus_di[-1]) else 20,
            'atr': new_atr_value if not np.isnan(new_atr_value) else 0.0,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'real_closes': real_closes,
            'volume_ratio': volume_ratio,
            'momentum_M1': momentum_M1_value
        }

        if security not in context.cache['technical_indicators']:
            context.cache['technical_indicators'][security] = {}
        context.cache['technical_indicators'][security].update(indicators_to_update)
        context.cache['last_update_date'] = current_date

        return context.cache['technical_indicators'][security]

    except Exception as e:
        log.error(f"更新 {security} 技术指标时出错: {str(e)}")
        default_indicators = {
            'bias': 0, 'adx': 25, 'plus_di': 20, 'minus_di': 20, 'atr': 0.0,
            'ma20': 0, 'ma60': 0, 'volatility': 0.02, 'historical_volatility': 0.02,
            'avg_volume': 1e6, 'trend_strength': 20, 'latest_price': 0,
            'volume_ratio': 1.0, 'real_closes': pd.Series([])
        }
        return context.cache.get('technical_indicators', {}).get(security, default_indicators)

# 修改原有的技术指标计算函数
def calculate_bias(context, security, n=24):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0.0

def calculate_adx(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        indicators = context.cache['technical_indicators'][security]
        return indicators['adx'], indicators['plus_di'], indicators['minus_di']
    except Exception as e:
        log.error(f"计算ADX时出错: {str(e)}")
        return 0.0, 0.0, 0.0

def calculate_atr(context, security, n=14):
    """使用缓存的技术指标"""
    try:
        return context.cache['technical_indicators'][security]['atr']
    except Exception as e:
        log.error(f"计算ATR时出错: {str(e)}")
        return 0.0

# 修改market_open函数
def market_open(context):
    """开盘时运行"""
    try:
        # 更新技术指标
        for security in context.stock_list:
            update_technical_indicators(context, security)

        # 检查是否需要建立初始仓位
        if context.initial_buy_needed and not context.initial_position_established:
            log.info("准备进行初始买入")
            execute_initial_buy(context)
            context.initial_buy_needed = False

        # 取消所有未完成订单
        cancel_all_orders(context)

        # 检查是否需要月度再平衡
        if should_rebalance(context):
            log.info("执行月度再平衡")
            rebalance_portfolio(context)  # 使用更复杂的再平衡函数

        # 更新市场状态
        for security in context.stock_list:
            old_state = context.market_states[security]
            new_state = get_market_state(context, security)
            if old_state != new_state:
                # 只在状态发生显著变化时记录
                if (old_state == 'oscillating' or new_state == 'oscillating') and abs(get_trend_strength(context, security)) > 20:
                    log.info(f"{security} 市场状态从 {old_state} 变为 {new_state}")
                context.market_states[security] = new_state

    except Exception as e:
        log.error(f"market_open出错: {str(e)}")

# 计算证券收益率
def get_security_returns(security):
    """
    计算证券的日收益率，使用真实价格
    参数:
        security: 证券代码
    返回:
        float: 当日收益率
    """
    # 获取当前价格
    current_data = get_current_data()
    current_price = current_data[security].last_price

    # 获取昨日收盘价和复权因子
    hist = attribute_history(security, 1, '1d', ['close', 'factor'])
    yesterday_price = hist['close'][0] / hist['factor'][0]

    # 计算收益率
    returns = (current_price - yesterday_price) / yesterday_price

    return returns

# 检查止损
def check_stop_loss(context):
    """检查止损条件"""
    for security in context.stock_list:
        returns = get_security_returns(security)
        stop_loss_line = calculate_dynamic_stop_loss(context, security)  # 使用更复杂的止损函数

        if returns <= -stop_loss_line:
            log.info(f"触发止损 - {security}: 收益率={returns:.2%}, 止损线={stop_loss_line:.2%}")
            position = context.portfolio.positions.get(security)
            if position and position.total_amount > 0:
                sell_amount = int(position.total_amount * 0.5)
                if sell_amount >= 100:
                    order(security, -sell_amount)
                    log.info(f"分批止损 - {security}: 卖出{sell_amount}股")
                else:
                    order_target(security, 0)
                    log.info(f"完全止损 - {security}: 清仓")
            context.stop_trading = True
            return

    portfolio_returns = context.portfolio.returns
    if portfolio_returns <= -context.portfolio_stop_loss:
        log.info(f"触发组合止损: 收益率={portfolio_returns:.2%}")
        context.stop_trading = True

def rebalance_portfolio(context):
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)

        # 记录原始备用资金比例
        original_reserve_pct = context.reserve_position_pct
        current_reserve_pct = original_reserve_pct

        # 优化：动态调整备用资金比例
        up_count = sum(1 for s in context.stock_list if context.market_states[s] == 'uptrend')
        down_count = sum(1 for s in context.stock_list if context.market_states[s] == 'downtrend')

        # 根据整体市场状况调整备用资金
        if up_count == len(context.stock_list):
            current_reserve_pct = max(0.1, original_reserve_pct * context.reserve_scale_down_factor)
            log.info(f'全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(context.stock_list):
            current_reserve_pct = min(0.5, original_reserve_pct * context.reserve_scale_up_factor)
            log.info(f'全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')

        # 动态调整各个标的的配置
        total_adjustment = 0  # 记录总调整幅度
        adjustments = {}  # 记录每个标的的调整幅度

        # 第一轮：计算理论调整幅度
        for security in context.stock_list:
            try:
                market_state = context.market_states[security]
                adjustment_factor, confidence = calculate_dynamic_allocation_factors(context, security)

                if market_state == 'uptrend':
                    # 上涨趋势的动态超配
                    base_extra = min(context.uptrend_base_extra, current_reserve_pct)
                    extra_weight = base_extra * adjustment_factor * confidence

                    # 添加趋势加速度判断
                    if context.cache['technical_indicators'][security]['trend_strength'] > 30:
                        extra_weight *= context.strong_trend_boost

                    adjustments[security] = extra_weight
                    total_adjustment += extra_weight

                elif market_state == 'downtrend':
                    # 下降趋势的动态减配
                    base_reduction = min(context.downtrend_base_reduction, weights[security] * context.downtrend_pct_limit)
                    reduction = base_reduction * adjustment_factor * confidence

                    # 添加反转信号判断
                    if context.cache['technical_indicators'][security]['bias'] < -context.bias_threshold[security]:
                        reduction *= context.oversold_reduction_factor

                    adjustments[security] = -reduction
                    total_adjustment -= reduction

                log.info(f"{security} 调整系数: {adjustment_factor:.2f}, 置信度: {confidence:.2f}")

            except Exception as e:
                log.error(f"计算{security}配置调整时出错: {str(e)}")
                adjustments[security] = 0

        # 第二轮：实际调整权重
        available_reserve = current_reserve_pct - 0.05  # 保留5%最小备用资金
        if total_adjustment > 0:
            # 等比例缩放调整幅度，确保不超过可用备用资金
            scale_factor = min(1.0, available_reserve / total_adjustment)
            for security in context.stock_list:
                if security in adjustments:
                    actual_adjustment = adjustments[security] * scale_factor
                    weights[security] += actual_adjustment
                    current_reserve_pct -= actual_adjustment

                    if abs(actual_adjustment) >= 0.001:  # 仅记录显著调整
                        log.info(f"{security} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")

        # 确保权重非负且总和不超过1
        weights = {s: max(0.0, w) for s, w in weights.items()}
        weight_sum = sum(float(w) for w in weights.values())

        if weight_sum > 1:
            scale = (1 - current_reserve_pct) / weight_sum
            weights = {s: w * scale for s, w in weights.items()}
            log.info(f"权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")

            # 再次验证调整后的权重总和
            final_weight_sum = sum(float(w) for w in weights.values())
            log.info(f"调整后权重总和: {final_weight_sum:.3f}")

        # 更新备用资金比例
        context.reserve_position_pct = current_reserve_pct
        log.info(f"调整后备用资金比例: {current_reserve_pct:.1%} (原始: {original_reserve_pct:.1%})")

        # 处理货基持仓
        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position and getattr(position, 'total_amount', 0) > 0:
                order_target(context.money_fund, 0)
                log.info(f"季度再平衡: 清空货基持仓 {position.total_amount}份")

        # 调整持仓
        total_position_value = context.portfolio.total_value * (1 - current_reserve_pct)
        for security in context.stock_list:
            target_value = total_position_value * weights[security]
            order_target_value(security, target_value)
            log.info(f"调整 {security} 目标市值至: {target_value:.2f}, 权重: {weights[security]:.2%}")

        # 更新最后再平衡时间
        context.last_rebalance_date = context.current_dt.date()

    except Exception as e:
        log.error(f"执行再平衡时发生错误: {str(e)}")
        raise  # 重新抛出异常，确保错误不被静默处理

def cancel_all_orders(context):
    """取消所有未完成的订单"""
    try:
        orders = get_open_orders()
        if orders:
            log.info(f"取消了 {len(orders)} 个未完成订单")
            for order in orders.values():
                cancel_order(order)
                log.debug(f"取消未完成订单: {order.security}, 委托价格: {order.price}, 委托数量: {order.amount}")
    except Exception as e:
        log.error(f"取消订单时出错: {str(e)}")

def execute_initial_buy(context):
    """执行初始建仓，加入市场状态判断和动态调整"""
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(context)

        # 记录原始备用资金比例
        original_reserve_pct = context.reserve_position_pct
        current_reserve_pct = original_reserve_pct

        # 分析整体市场状况
        up_count = 0
        down_count = 0
        for security in context.stock_list:
            market_state = get_market_state(context, security)
            if market_state == 'uptrend':
                up_count += 1
            elif market_state == 'downtrend':
                down_count += 1

        # 根据整体市场状况调整备用资金
        if up_count == len(context.stock_list):
            # 全面上涨行情，降低备用资金
            current_reserve_pct = max(0.1, original_reserve_pct * context.reserve_scale_down_factor)
            log.info(f'初始建仓：全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(context.stock_list):
            # 全面下跌行情，提高备用资金
            current_reserve_pct = min(0.5, original_reserve_pct * context.reserve_scale_up_factor)
            log.info(f'初始建仓：全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')

        # 动态调整各个标的的配置
        total_adjustment = 0  # 记录总调整幅度
        adjustments = {}  # 记录每个标的的调整幅度

        # 计算理论调整幅度
        for security in context.stock_list:
            try:
                market_state = context.market_states[security]
                adjustment_factor, confidence = calculate_dynamic_allocation_factors(context, security)

                if market_state == 'uptrend':
                    # 上涨趋势的动态超配
                    base_extra = min(context.uptrend_base_extra, current_reserve_pct)
                    extra_weight = base_extra * adjustment_factor * confidence

                    # 添加趋势加速度判断
                    if context.cache['technical_indicators'][security]['trend_strength'] > 30:
                        extra_weight *= context.strong_trend_boost

                    adjustments[security] = extra_weight
                    total_adjustment += extra_weight

                elif market_state == 'downtrend':
                    # 下降趋势的动态减配
                    base_reduction = min(context.downtrend_base_reduction, weights[security] * context.downtrend_pct_limit)
                    reduction = base_reduction * adjustment_factor * confidence

                    # 添加反转信号判断
                    if context.cache['technical_indicators'][security]['bias'] < -context.bias_threshold[security]:
                        reduction *= context.oversold_reduction_factor

                    adjustments[security] = -reduction
                    total_adjustment -= reduction

                log.info(f"初始建仓 {security} 调整系数: {adjustment_factor:.2f}, 置信度: {confidence:.2f}")

            except Exception as e:
                log.error(f"计算{security}配置调整时出错: {str(e)}")
                adjustments[security] = 0

        # 实际调整权重
        available_reserve = current_reserve_pct - 0.05  # 保留5%最小备用资金
        if total_adjustment > 0:
            # 等比例缩放调整幅度，确保不超过可用备用资金
            scale_factor = min(1.0, available_reserve / total_adjustment)
            for security in context.stock_list:
                if security in adjustments:
                    actual_adjustment = adjustments[security] * scale_factor
                    weights[security] += actual_adjustment
                    current_reserve_pct -= actual_adjustment

                    if abs(actual_adjustment) >= 0.001:  # 仅记录显著调整
                        log.info(f"初始建仓 {security} {'超配' if actual_adjustment > 0 else '减配'} {abs(actual_adjustment):.1%}")

        # 确保权重非负且总和不超过1
        weights = {s: max(0.0, w) for s, w in weights.items()}
        weight_sum = sum(float(w) for w in weights.values())

        if weight_sum > 1:
            scale = (1 - current_reserve_pct) / weight_sum
            weights = {s: w * scale for s, w in weights.items()}
            log.info(f"初始建仓：权重总和超过1，进行等比例缩减，缩减系数: {scale:.3f}")

        # 更新备用资金比例
        context.reserve_position_pct = current_reserve_pct
        log.info(f"初始建仓：调整后备用资金比例: {current_reserve_pct:.1%} (原始: {original_reserve_pct:.1%})")

        # 执行建仓
        total_value = context.portfolio.total_value
        available_cash = context.portfolio.available_cash

        for security in context.stock_list:
            try:
                # 计算目标金额
                target_value = total_value * (1 - current_reserve_pct) * weights.get(security, 0)

                # 获取当前价格
                current_data = get_current_data()[security]
                if current_data.paused:
                    log.warning(f"{security} 当前暂停交易，跳过建仓")
                    continue

                current_price = current_data.last_price

                # 计算购买数量（确保是100的整数倍）
                amount = int(target_value / current_price / 100) * 100

                if amount >= 100:  # 确保购买数量至少为100股
                    order_value = amount * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        order(security, amount)
                        log.info(f"初始建仓 {security}: 数量={amount}, 价格={current_price:.3f}, 金额={order_value:.2f}, 权重={weights[security]:.1%}")
                    else:
                        log.warning(f"初始建仓 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    log.warning(f"初始建仓 {security}: 计算购买数量小于100股，跳过购买")
            except Exception as e:
                log.error(f"处理标的 {security} 时发生错误: {str(e)}")
                continue

        log.info("初始建仓完成")
        context.initial_position_established = True
    except Exception as e:
        log.error(f"执行初始建仓时发生错误: {str(e)}")
        raise

def check_fund_income(context):
    """
    检查货基收益情况
    """
    try:
        # 只在收盘前检查一次
        if context.current_dt.time() != datetime.time(15, 10):
            return

        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position.total_amount > 0:
                current_data = get_current_data()[context.money_fund]
                current_price = current_data.last_price

                cost = position.avg_cost * position.total_amount
                current_value = current_price * position.total_amount
                daily_return = (current_value - cost) / cost

                # 只在收益率超过0.01%时记录日志
                if abs(daily_return) > 0.0001:
                    log.info(f"货基当日收益: {daily_return:.4%}")

    except Exception as e:
        log.error(f"检查货基收益时发生错误: {str(e)}")

def get_market_state(context, security):
    """
    根据技术指标判断市场状态
    返回: 'uptrend', 'downtrend', 或 'oscillating'
    """
    try:
        # 更新技术指标
        indicators = update_technical_indicators(context, security)

        # 获取BIAS和ADX值
        bias = indicators.get('bias', 0)  # 使用get方法安全获取bias值
        adx = indicators.get('adx', 0)    # 使用get方法安全获取adx值
        plus_di = indicators.get('plus_di', 0)  # 使用get方法安全获取plus_di值
        minus_di = indicators.get('minus_di', 0)  # 使用get方法安全获取minus_di值

        # 获取该证券的BIAS阈值
        bias_threshold = context.bias_threshold[security]

        # 趋势判断标准
        TREND_THRESHOLD = 35  # ADX高于35即为趋势
        # 震荡市判定标准放宽
        if adx < TREND_THRESHOLD or abs(bias) < bias_threshold * context.bias_oscillation_scale_factor:
            return 'oscillating'

        # 判断市场状态
        if adx > TREND_THRESHOLD:  # 存在明显趋势
            if plus_di > minus_di:  # 上升趋势
                if bias > bias_threshold:  # BIAS过高，可能超买
                    return 'oscillating'
                return 'uptrend'
            else:  # 下降趋势
                if bias < -bias_threshold:  # BIAS过低，可能超卖
                    return 'oscillating'
                return 'downtrend'
        else:  # ADX较低，无明显趋势
            if abs(bias) > bias_threshold:  # BIAS超出阈值
                if bias > bias_threshold:  # 可能超买
                    return 'downtrend'
                else:  # 可能超卖
                    return 'uptrend'
            return 'oscillating'  # 震荡市场

    except Exception as e:
        log.error(f"判断市场状态时出错: {str(e)}")
        return 'oscillating'  # 发生错误时默认返回震荡状态

def calculate_price_trend(context, security, period=20):
    """
    计算价格趋势
    返回: float, 正值表示上涨趋势，负值表示下跌趋势
    """
    try:
        # 获取历史价格数据
        hist_data = attribute_history(security, period, '1d', ['close'])
        prices = hist_data['close']

        # 计算价格变化率
        price_change = (prices[-1] - prices[0]) / prices[0]

        return price_change

    except Exception as e:
        log.error(f"计算价格趋势时出错: {str(e)}")
        return 0.0

def calculate_volume_trend(context, security, period=20):
    """
    计算成交量趋势
    返回: float, 大于1表示放量，小于1表示缩量
    """
    try:
        # 获取历史成交量数据
        hist_data = attribute_history(security, period, '1d', ['volume'])
        volumes = hist_data['volume']

        # 计算最近5日平均成交量与20日平均成交量的比值
        recent_avg = np.mean(volumes[-5:])
        total_avg = np.mean(volumes)

        return recent_avg / total_avg if total_avg > 0 else 1.0

    except Exception as e:
        log.error(f"计算成交量趋势时出错: {str(e)}")
        return 1.0

def calculate_volatility(context, security, period=20):
    prices = context.cache['technical_indicators'][security]['real_closes']
    returns = prices.pct_change().dropna()
    return returns.rolling(period).std().iloc[-1]

def get_volume_ratio(context, security, period=20):
    """计算量比，使用缓存数据"""
    try:
        # 使用缓存的量比
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'volume_ratio' in indicators:
            return indicators['volume_ratio']

        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        return context.cache['technical_indicators'][security]['volume_ratio']
    except Exception as e:
        log.error(f"计算量比时出错: {str(e)}")
        return 1.0  # 默认量比为1

def get_bias(context, security):
    """计算BIAS，使用缓存数据"""
    try:
        # 使用缓存的BIAS
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'bias' in indicators:
            return indicators['bias']

        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        return context.cache['technical_indicators'][security]['bias']
    except Exception as e:
        log.error(f"计算BIAS时出错: {str(e)}")
        return 0  # 默认BIAS为0

def get_trend_strength(context, security, period=20):
    """计算趋势强度，使用缓存数据"""
    try:
        # 使用缓存的趋势强度
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            return indicators['trend_strength']

        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            return indicators['trend_strength']

        # 如果仍然没有趋势强度，返回默认值
        return 20  # 默认中等趋势
    except Exception as e:
        log.error(f"计算趋势强度时出错: {str(e)}")
        return 20  # 默认中等趋势

def get_avg_volume(context, security, period=20):
    """计算平均成交量，使用缓存数据"""
    try:
        # 使用缓存的平均成交量
        indicators = context.cache['technical_indicators'].get(security)
        if indicators and 'avg_volume' in indicators:
            return indicators['avg_volume']

        # 如果缓存不存在，更新技术指标
        update_technical_indicators(context, security)
        return context.cache['technical_indicators'][security]['avg_volume']
    except Exception as e:
        log.error(f"计算平均成交量时出错: {str(e)}")
        return 1e6  # 默认值

def order_with_fund(context, security, amount):
    current_data = get_current_data()[security]
    current_price = current_data.last_price
    required_cash = amount * current_price
    if context.portfolio.available_cash < required_cash:
        # 需要卖出货基
        fund_data = get_current_data()[context.money_fund]
        fund_price = fund_data.last_price
        sell_amount = int((required_cash - context.portfolio.available_cash) / fund_price / 100) * 100
        if sell_amount > 0:
            order_target(context.money_fund, context.portfolio.positions.get(context.money_fund, 0) - sell_amount)
    order(security, amount)

def calculate_dynamic_stop_loss(context, security):
    """计算动态止损线"""
    try:
        # 获取市场状态和波动率
        market_state = context.market_states[security]
        volatility = calculate_volatility(context, security)

        # 基础止损线
        base_stop_loss = context.single_stock_stop_loss

        # 根据市场状态调整
        if market_state == 'uptrend':
            stop_loss_factor = 1.2  # 上涨趋势放宽止损
        elif market_state == 'downtrend':
            stop_loss_factor = 0.8  # 下跌趋势收紧止损
        else:
            stop_loss_factor = 1.0  # 震荡市保持基础止损

        # 根据波动率调整
        volatility_factor = 1.0 + (volatility - 0.02) * 2  # 波动率每增加1%，止损放宽2%
        volatility_factor = max(0.8, min(1.5, volatility_factor))  # 限制调整范围

        # 计算最终止损线
        final_stop_loss = base_stop_loss * stop_loss_factor * volatility_factor

        return max(0.03, min(0.08, final_stop_loss))  # 限制止损线在3%-8%之间

    except Exception as e:
        log.error(f"计算动态止损线时出错: {str(e)}")
        return context.single_stock_stop_loss

def calculate_dynamic_allocation_factors(context, security):
    """
    计算动态配置调整因子
    返回: (float, float) - (调整系数, 置信度)
    """
    try:
        indicators = context.cache['technical_indicators'].get(security)
        if not indicators:
            update_technical_indicators(context, security)
            indicators = context.cache['technical_indicators'].get(security)

        # 获取关键指标，使用get方法安全获取
        trend_strength = indicators.get('trend_strength', 20)  # 默认中等趋势
        volatility = indicators.get('volatility', 0.02)  # 默认波动率
        volume_ratio = indicators.get('volume_ratio', 1.0)  # 默认量比
        bias = indicators.get('bias', 0)  # 默认BIAS
        adx = indicators.get('adx', 25)  # 默认ADX
        plus_di = indicators.get('plus_di', 20)  # 默认+DI
        minus_di = indicators.get('minus_di', 20)  # 默认-DI

        # 计算趋势强度因子 (0.5 ~ 2.0)
        trend_factor = min(2.0, max(0.5, trend_strength / 20))

        # 计算趋势方向因子 (0.8 ~ 1.5)
        direction_factor = 1.0
        if plus_di > minus_di:
            direction_factor = min(1.5, max(1.0, 1 + (plus_di - minus_di) / 100))
        else:
            direction_factor = min(1.0, max(0.8, 1 - (minus_di - plus_di) / 100))

        # 计算波动率因子 (0.6 ~ 1.5)
        vol_base = 0.02  # 基准波动率
        if volatility < vol_base:
            volatility_factor = min(1.5, vol_base / volatility)
        else:
            volatility_factor = max(0.6, vol_base / volatility)

        # 计算成交量因子 (0.7 ~ 1.8)
        if volume_ratio > 1:
            volume_factor = min(1.8, 1 + (volume_ratio - 1) * 0.5)
        else:
            volume_factor = max(0.7, 1 - (1 - volume_ratio) * 0.5)

        # 计算BIAS影响因子 (0.6 ~ 1.6)
        bias_threshold = context.bias_threshold[security]
        if abs(bias) < bias_threshold * 0.5:  # BIAS在合理范围内，更积极调整
            bias_factor = 1.6 - abs(bias) / bias_threshold
        else:  # BIAS偏离较大，谨慎调整
            bias_factor = max(0.6, 1 - abs(bias) / bias_threshold)

        # 计算市场趋势持续性
        trend_persistence = min(1.5, adx / context.alloc_persistence_adx_base)

        # 综合计算调整系数 - 使用优化后的权重
        adjustment_factor = (
            trend_factor * context.weight_trend +
            direction_factor * context.weight_direction +
            volatility_factor * context.weight_volatility +
            volume_factor * context.weight_volume +
            bias_factor * context.weight_bias
        ) * trend_persistence  # 使用趋势持续性作为整体调节因子

        # 计算置信度 (0 ~ 1)
        confidence = min(1.0, (
            (trend_strength / context.conf_trend_base) * context.conf_trend_weight +
            (1 - abs(bias) / (bias_threshold * context.conf_bias_mult)) * context.conf_bias_weight +
            (adx / context.conf_adx_base) * context.conf_adx_weight +
            (volume_ratio - context.conf_vol_offset) * context.conf_vol_weight
        ))

        # 根据市场状态调整最终系数
        market_state = context.market_states[security]
        if market_state == 'uptrend':
            # 上涨趋势时，如果指标都支持，可以更激进
            if confidence > 0.7 and trend_factor > 1.2:
                adjustment_factor *= context.strong_trend_boost
        elif market_state == 'downtrend':
            # 下跌趋势时，如果指标显示反转可能，保持灵活
            if confidence > 0.6 and bias_factor > 1.2:
                adjustment_factor *= context.oversold_reduction_factor

        # 确保最终调整系数在合理范围内
        adjustment_factor = min(2.5, max(0.5, adjustment_factor))

        # 记录详细的调整因子信息
        if adjustment_factor > 1.5 or adjustment_factor < 0.7:
            log.info(f"{security} 调整因子详情:")
            log.info(f"趋势因子: {trend_factor:.2f}, 方向因子: {direction_factor:.2f}")
            log.info(f"波动率因子: {volatility_factor:.2f}, 成交量因子: {volume_factor:.2f}")
            log.info(f"BIAS因子: {bias_factor:.2f}, 趋势持续性: {trend_persistence:.2f}")

        return adjustment_factor, confidence

    except Exception as e:
        log.error(f"计算动态配置因子时出错: {str(e)}")
        return 1.0, 0.5  # 出错时返回默认值

def trade_money_fund(context):
    """交易货币基金"""
    try:
        # 只在开盘时执行一次
        if context.current_dt.time() != datetime.time(9, 32):
            return

        # 计算大额闲置资金阈值
        total_value = context.portfolio.total_value
        min_cash_buffer = total_value * 0.01  # 1%现金缓冲
        available_cash = context.portfolio.available_cash - min_cash_buffer

        # 只有当闲置资金大于1万元时才买入货基
        if available_cash > 10000:
            current_data = get_current_data()[context.money_fund]
            if current_data and not current_data.paused:
                current_price = current_data.last_price
                position = context.portfolio.positions.get(context.money_fund)
                current_amount = position.total_amount if position is not None else 0
                target_amount = int(available_cash / (current_price * 100)) * 100
                if abs(target_amount - current_amount) > 1000:
                    order_target(context.money_fund, target_amount)
                    log.info(f"货基调整: {target_amount-current_amount:+d}份")
        # 其余情况不动
    except Exception as e:
        log.error(f"货基交易过程中发生错误: {str(e)}")

def monitor_strategy_performance(context):
    """监控策略性能"""
    try:
        # 计算当日收益
        daily_return = context.portfolio.returns

        # 计算波动率
        portfolio_volatility = calculate_portfolio_volatility(context)

        # 计算夏普比率
        sharpe_ratio = daily_return / portfolio_volatility if portfolio_volatility > 0 else 0

        # 检查风险指标
        if daily_return < -0.02:  # 单日亏损超过2%
            log.warning(f"策略单日亏损较大: {daily_return:.2%}")

        if portfolio_volatility > 0.03:  # 波动率超过3%
            log.warning(f"策略波动率较高: {portfolio_volatility:.2%}")

        if sharpe_ratio < 0.5:  # 夏普比率低于0.5
            log.warning(f"策略夏普比率较低: {sharpe_ratio:.2f}")

        # 记录性能指标
        log.info(f"策略性能指标 - 日收益: {daily_return:.2%}, 波动率: {portfolio_volatility:.2%}, 夏普比率: {sharpe_ratio:.2f}")

    except Exception as e:
        log.error(f"监控策略性能时出错: {str(e)}")

def calculate_portfolio_volatility(context):
    """计算投资组合的波动率"""
    try:
        # 获取每个标的的波动率
        volatilities = []
        for security in context.stock_list:
            indicators = context.cache['technical_indicators'].get(security)
            if indicators and 'volatility' in indicators:
                volatilities.append(indicators['volatility'])

        # 计算平均波动率
        if volatilities:
            return sum(volatilities) / len(volatilities)
        return 0.02  # 默认波动率
    except Exception as e:
        log.error(f"计算投资组合波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def calculate_risk_parity_weights(context):
    """计算风险平价权重"""
    try:
        # 初始化波动率字典
        volatilities = {}

        # 计算每个ETF的60日波动率
        for security in context.stock_list:
            try:
                # 获取过去60个交易日的收盘价，使用attribute_history替代get_price，自动遵循avoid_future_data设置
                price_data = attribute_history(security, 60, '1d', ['close'], df=True)
                close = price_data['close']

                # 计算日收益率
                returns = close.pct_change().dropna()

                # 计算波动率（标准差）
                volatility = returns.std()
                volatilities[security] = volatility

            except Exception as e:
                log.error(f"计算 {security} 波动率时出错: {str(e)}")
                volatilities[security] = 0.02  # 使用默认波动率

        # 计算权重：1/波动率 / 所有(1/波动率)之和
        inv_vol_sum = sum(1.0 / vol for vol in volatilities.values())
        weights = {security: (1.0 / vol) / inv_vol_sum for security, vol in volatilities.items()}

        # 优化：将权重转换为整数百分比
        total_pct = 100
        int_weights = {}
        remaining = total_pct

        # 先将权重转换为整数百分比（向下取整）
        for security, weight in weights.items():
            int_weight = int(weight * 100)
            int_weights[security] = int_weight
            remaining -= int_weight

        # 将剩余的百分比按原权重比例分配
        if remaining > 0:
            # 按原始权重排序，权重大的优先获得剩余百分比
            sorted_securities = sorted(weights.keys(), key=lambda x: weights[x], reverse=True)
            for i in range(remaining):
                security = sorted_securities[i % len(sorted_securities)]
                int_weights[security] += 1

        # 转换回小数形式
        rounded_weights = {security: weight / 100.0 for security, weight in int_weights.items()}

        # 输出取整后的权重信息（改为debug级别）
        weight_str = ', '.join([f"{security}: {weight:.0%}" for security, weight in rounded_weights.items()])
        log.debug(f"风险平价权重计算结果: {weight_str}")

        return rounded_weights

    except Exception as e:
        log.error(f"计算风险平价权重时出错: {str(e)}")
        # 出错时返回等权重
        equal_weight = 1.0 / len(context.stock_list)
        return {security: equal_weight for security in context.stock_list}

def apply_momentum_overlay(context):
    """应用动量叠加策略"""
    try:
        # 检查是否已经执行过本周的动量调整
        current_date = context.current_dt.date()
        if context.cache['last_momentum_check'] == current_date:
            return

        # 更新技术指标
        for security in context.stock_list:
            update_technical_indicators(context, security)

        # 获取基础风险平价权重
        base_weights = calculate_risk_parity_weights(context)

        # 计算实际可用的备用金
        total_value = context.portfolio.total_value
        available_cash = context.portfolio.available_cash

        # 计算货基市值
        money_fund_value = 0
        if context.money_fund in context.portfolio.positions:
            position = context.portfolio.positions[context.money_fund]
            if position and position.total_amount > 0:
                current_data = get_current_data()[context.money_fund]
                if current_data and not current_data.paused:
                    money_fund_value = position.total_amount * current_data.last_price

        # 计算实际可用的备用金总额
        actual_reserve_value = available_cash + money_fund_value
        actual_reserve_pct = actual_reserve_value / total_value if total_value > 0 else 0

        # 设置最大可用备用金（使用实际值的一半）
        max_total_boost = min(actual_reserve_pct / 2, context.max_momentum_allocation_pct)

        # 识别动量资产 - 使用M1动量因子
        momentum_assets = []
        for security in context.stock_list:
            try:
                indicators = context.cache['technical_indicators'].get(security)
                if not indicators:
                    log.warning(f"{security} 没有技术指标数据，跳过动量判断")
                    continue

                # 检查必要的指标是否存在
                if 'latest_price' not in indicators or 'momentum_M1' not in indicators:
                    log.warning(f"{security} 缺少必要的技术指标数据，跳过动量判断")
                    continue

                # 使用M1动量因子判断动量信号
                momentum_strength = indicators.get('momentum_M1', 0.0)
                if momentum_strength > context.momentum_threshold:
                    momentum_assets.append((security, momentum_strength))
                    log.info(f"{security} 满足M1动量条件: 价格={indicators['latest_price']:.2f}, M1动量={momentum_strength:.2%}")
            except Exception as e:
                log.error(f"处理{security}的动量判断时出错: {str(e)}")
                continue

        # 按动量强度排序
        momentum_assets.sort(key=lambda x: x[1], reverse=True)

        # 初始化最终权重
        final_weights = base_weights.copy()
        total_boost_weight = 0.0
        boost_allocations = {}  # 记录每个资产的增强分配

        # 计算动量调整
        for security, momentum_strength in momentum_assets:
            # 计算该资产的权重增量
            base_weight = float(base_weights[security])
            boost = base_weight * (context.momentum_boost_factor - 1)

            # 根据动量强度调整增强系数
            strength_factor = min(1.5, 1 + momentum_strength * 2)
            adjusted_boost = boost * strength_factor

            # 检查是否超过最大增强限制
            if total_boost_weight + adjusted_boost <= max_total_boost:
                final_weights[security] = base_weight + adjusted_boost
                total_boost_weight += adjusted_boost
                boost_allocations[security] = adjusted_boost
                log.info(f"动量增强 {security}: 基础权重 {base_weight:.1%} -> {final_weights[security]:.1%}, M1动量 {momentum_strength:.2%}")
            else:
                # 如果备用金不足，按比例缩减
                remaining_boost = max_total_boost - total_boost_weight
                if remaining_boost > 0:
                    scale = remaining_boost / adjusted_boost
                    final_weights[security] = base_weight + (adjusted_boost * scale)
                    total_boost_weight = max_total_boost
                    boost_allocations[security] = adjusted_boost * scale
                    log.info(f"动量增强 {security}: 基础权重 {base_weight:.1%} -> {final_weights[security]:.1%}, M1动量 {momentum_strength:.2%} (已缩减)")
                break

        # 计算目标总市值（不包括备用金）
        target_total_value = total_value * (1 - total_boost_weight)

        # 权重归一化
        current_total_weight = sum(float(w) for w in final_weights.values())
        if abs(current_total_weight - 1.0) > 0.001:
            scale = 1.0 / current_total_weight
            final_weights = {sec: float(w) * scale for sec, w in final_weights.items()}
            log.info(f"权重归一化: 缩放系数 {scale:.3f}")

        # 计算需要的额外资金
        required_additional_cash = 0
        for security in context.stock_list:
            current_value = context.portfolio.positions[security].value if security in context.portfolio.positions else 0
            target_value = target_total_value * float(final_weights[security])
            if target_value > current_value:
                required_additional_cash += (target_value - current_value)

        # 如果需要额外资金，先处理货基
        if required_additional_cash > available_cash and money_fund_value > 0:
            # 计算需要卖出的货基数量
            current_data = get_current_data()[context.money_fund]
            if current_data and not current_data.paused:
                required_fund_amount = int((required_additional_cash - available_cash) / current_data.last_price / 100) * 100
                if required_fund_amount > 0:
                    current_fund_amount = context.portfolio.positions[context.money_fund].total_amount
                    sell_amount = min(required_fund_amount, current_fund_amount)
                    if sell_amount > 0:
                        order(context.money_fund, -sell_amount)
                        log.info(f"卖出货基 {sell_amount} 份以提供流动")
                        # 等待货基卖出完成
                        return  # 下次再执行股票调仓

        # 执行股票调仓
        for security in context.stock_list:
            target_value = target_total_value * float(final_weights[security])
            order_target_value(security, target_value)
            log.info(f"动量调整 {security}: 目标市值 {target_value:.2f}, 权重 {final_weights[security]:.1%}")

        # 更新动量检查时间
        context.cache['last_momentum_check'] = current_date

    except Exception as e:
        log.error(f"应用动量叠加策略时出错: {str(e)}")
        import traceback
        log.error(f"错误详情: {traceback.format_exc()}")
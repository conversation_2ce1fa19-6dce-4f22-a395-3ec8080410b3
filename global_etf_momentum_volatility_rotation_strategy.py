# 导入所需的库
from jqdata import *
import numpy as np
import pandas as pd
import datetime

# 初始化函数
def initialize(context):
    """初始化函数，设置基本参数和运行环境"""
    # 设置基准
    set_benchmark('000300.XSHG')

    # 设置交易成本
    set_order_cost(OrderCost(open_tax=0, close_tax=0.001, open_commission=0.0003, close_commission=0.0003, close_today_commission=0), type='stock')

    # 设置调仓周期（每月第一个交易日）
    run_monthly(rebalance, 1, 'open')

    # 定义ETF标的池
    context.etf_pool = [
        '510300.XSHG',  # 沪深300ETF
        '513100.XSHG',  # 纳斯达克100ETF
        '513000.XSHG',  # 日经225ETF
        '513730.XSHG',  # 东南亚科技ETF
        '513080.XSHG',  # 法国CAC40ETF
        '513030.XSHG',  # 德国DAX ETF
        '518880.XSHG'   # 黄金ETF
    ]
    context.cash_etf = '511880.XSHG'  # 货币ETF

    # 设置回测期间
    set_start_date('2015-01-01')
    set_end_date('2024-01-01')

    # 设置动量周期（交易日）
    context.momentum_periods = [21, 63, 126]  # 分别近似1个月、3个月、6个月的交易日
    context.momentum_weights = [0.4, 0.3, 0.3]  # 对应上述周期的动量权重

    # 设置波动率计算周期
    context.volatility_period = 60  # 波动率计算周期（约3个月）

    # 设置因子权重
    context.factor_weights = {'momentum': 0.8, 'volatility': 0.2}  # 最终综合评分中，动量和波动率的权重

    # 设置运行时需要的变量
    context.last_rebalance_date = None  # 上次再平衡时间

# 每月调仓函数
def rebalance(context):
    """每月调仓函数，根据动量和波动率选择ETF"""
    # 获取当前日期
    current_dt = context.current_dt
    log.info(f"执行调仓，当前日期: {current_dt.strftime('%Y-%m-%d')}")

    # 1. 计算所有ETF的综合评分
    etf_factors = calculate_etf_factors(context, context.etf_pool)

    # 如果没有有效的ETF因子数据，则全部资金买入货币基金
    if etf_factors.empty or len(etf_factors) == 0:
        log.info("没有有效的ETF因子数据，全部资金买入货币基金")
        order_target_value(context.cash_etf, context.portfolio.total_value)
        for etf in context.etf_pool:
            if context.portfolio.positions.get(etf, None):
                order_target(etf, 0)
        context.last_rebalance_date = current_dt
        return

    # 打印因子值
    log.info("ETF因子值:")
    for etf, row in etf_factors.iterrows():
        log.info(f"{etf} - 动量: {row['momentum_score']:.4f}, 波动率: {row['volatility_score']:.4f}, 最终评分: {row['final_score']:.4f}")

    # 2. 选择前3名ETF
    top3 = etf_factors.sort_values('final_score', ascending=False).head(3)

    # 由于使用排名法，分数已经在0-1之间，不需要处理负分
    # 但为了保持代码一致性，我们仍然创建adjusted_score列
    top3['adjusted_score'] = top3['final_score']
    total_score = top3['adjusted_score'].sum()

    # 3. 权重分配
    if total_score == 0:
        # 全部配置货币ETF
        log.info("所有ETF的调整分数都为0，全部资金买入货币基金")
        target_portfolio = {context.cash_etf: 1.0}
    else:
        target_portfolio = {}
        for etf, row in top3.iterrows():
            weight = row['adjusted_score'] / total_score
            target_portfolio[etf] = weight
            log.info(f"选择 {etf}, 权重: {weight:.4f}")

    # 4. 执行调仓
    adjust_portfolio(context, target_portfolio)
    context.last_rebalance_date = current_dt

# 辅助函数：计算ETF因子
def calculate_etf_factors(context, etf_list):
    """计算ETF的因子值"""
    # 创建一个空的DataFrame来存储因子值
    columns = ['momentum_score', 'volatility_score', 'momentum_rank', 'volatility_rank', 'final_score']
    etf_factors = pd.DataFrame(index=etf_list, columns=columns)

    # 遍历ETF列表，计算各因子值
    valid_etfs = []
    for etf in etf_list:
        # 检查ETF是否已上市
        if not is_etf_listed(etf, context.current_dt):
            log.info(f"{etf} 尚未上市，跳过")
            continue

        # 计算动量因子
        try:
            momentum_score = calculate_momentum(etf, context.current_dt, context.momentum_periods, context.momentum_weights)
            if np.isnan(momentum_score) or np.isinf(momentum_score):
                log.info(f"{etf} 动量因子计算无效（数据不足或异常），跳过")
                continue
            etf_factors.loc[etf, 'momentum_score'] = momentum_score
        except Exception as e:
            log.error(f"计算 {etf} 动量因子时出错: {str(e)}")
            continue

        # 计算波动率因子
        try:
            volatility = calculate_volatility(etf, context.current_dt, context.volatility_period)
            if np.isnan(volatility) or np.isinf(volatility):
                log.info(f"{etf} 波动率因子计算无效（数据不足或异常），跳过")
                continue
            etf_factors.loc[etf, 'volatility_score'] = volatility
        except Exception as e:
            log.error(f"计算 {etf} 波动率因子时出错: {str(e)}")
            continue

        valid_etfs.append(etf)

    # 只保留有效的ETF
    etf_factors = etf_factors.loc[valid_etfs]

    # 如果没有有效的ETF，返回空DataFrame
    if len(valid_etfs) == 0:
        return pd.DataFrame()

    # 使用排名法替代Z-score
    # 对动量进行排名（越高越好）
    etf_factors['momentum_rank'] = etf_factors['momentum_score'].rank(pct=True)

    # 对波动率进行排名（越低越好，所以用1减去排名）
    etf_factors['volatility_rank'] = 1 - etf_factors['volatility_score'].rank(pct=True)

    # 计算最终评分：加权组合动量排名和波动率排名
    etf_factors['final_score'] = (
        context.factor_weights['momentum'] * etf_factors['momentum_rank'] +
        context.factor_weights['volatility'] * etf_factors['volatility_rank']
    )

    # 打印排名信息
    log.info("ETF排名情况:")
    for etf, row in etf_factors.iterrows():
        log.info(f"{etf} - 动量排名: {row['momentum_rank']:.4f}, 波动率排名: {row['volatility_rank']:.4f}, 最终评分: {row['final_score']:.4f}")

    return etf_factors

# 辅助函数：检查ETF是否已上市
def is_etf_listed(etf_code, current_dt):
    """检查ETF是否已上市"""
    try:
        security_info = get_security_info(etf_code)
        # 将current_dt转换为date类型进行比较
        current_date = current_dt.date() if hasattr(current_dt, 'date') else current_dt
        return security_info.start_date <= current_date
    except Exception as e:
        log.error(f"检查 {etf_code} 上市状态时出错: {str(e)}")
        return False

# 辅助函数：计算动量因子
def calculate_momentum(etf_code, current_dt, periods, weights):
    """计算单个ETF的动量因子"""
    try:
        # 获取历史价格（最多6个月数据）
        max_period = max(periods)
        prices = get_price(etf_code,
                          end_date=current_dt,
                          frequency='daily',
                          fields=['close'],
                          count=max_period + 1,  # 多取一天用于计算收益率
                          skip_paused=True)

        if prices is None or len(prices) < 21:  # 至少需要1个月数据
            log.info(f"{etf_code} 数据不足，无法计算动量")
            return np.nan

        close_prices = prices['close']

        # 计算不同周期动量
        momentum_values = []
        valid_weights = []

        for period, weight in zip(periods, weights):
            if len(close_prices) > period:
                # 计算收益率
                momentum = (close_prices.iloc[-1] / close_prices.iloc[-period-1]) - 1
                momentum_values.append(momentum)
                valid_weights.append(weight)
            else:
                log.info(f"{etf_code} 数据长度不足 {period} 天，跳过该周期")

        # 如果没有有效的动量值，返回NaN
        if len(momentum_values) == 0:
            return np.nan

        # 归一化权重
        valid_weights = [w/sum(valid_weights) for w in valid_weights]

        # 计算加权动量分数
        momentum_score = sum(m * w for m, w in zip(momentum_values, valid_weights))

        return momentum_score

    except Exception as e:
        log.error(f"计算 {etf_code} 动量分数时出错: {str(e)}")
        return np.nan

# 辅助函数：计算波动率因子
def calculate_volatility(etf_code, current_dt, period):
    """计算单个ETF的波动率"""
    try:
        # 获取历史价格数据
        prices = get_price(etf_code,
                          end_date=current_dt,
                          frequency='daily',
                          fields=['close'],
                          count=period + 1,  # 多取一天用于计算收益率
                          skip_paused=True)

        if prices is None or len(prices) < period:
            log.info(f"{etf_code} 数据长度不足 {period} 天，无法计算波动率")
            return np.nan

        close_prices = prices['close']

        # 计算日收益率
        returns = close_prices.pct_change().dropna()

        # 计算波动率（标准差）
        volatility = returns.std()

        return volatility

    except Exception as e:
        log.error(f"计算 {etf_code} 波动率时出错: {str(e)}")
        return np.nan

# 注意：由于我们使用了排名法替代Z-score，此函数已不再使用
# 但为了保持代码完整性，我们保留函数定义但简化其实现
def z_score_and_winsorize(series, limits):
    """此函数已被排名法替代，保留仅为兼容性"""
    log.info("Z-score函数已被排名法替代，不应被调用")
    return series

# 辅助函数：执行调仓
def adjust_portfolio(context, target_portfolio):
    """执行调仓"""
    log.info("执行调仓操作:")

    # 清仓不在目标组合中的持仓
    for etf in context.portfolio.positions:
        if etf not in target_portfolio:
            order_target(etf, 0)
            log.info(f"清仓 {etf}")

    # 调整目标仓位
    for etf, weight in target_portfolio.items():
        if weight > 0:
            order_target_percent(etf, weight)
            log.info(f"调整 {etf} 至目标权重: {weight:.4f}")
